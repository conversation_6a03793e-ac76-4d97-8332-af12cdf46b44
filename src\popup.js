document.addEventListener('DOMContentLoaded', function() {
  const startDateInput = document.getElementById('startDate');
  const endDateInput = document.getElementById('endDate');
  const downloadBtn = document.getElementById('downloadBtn');
  const settingsBtn = document.getElementById('settingsBtn');
  const progressBar = document.getElementById('progressBar');
  const progressText = document.getElementById('progressText');
  const progressDiv = document.getElementById('progress');
  const statusDiv = document.getElementById('status');
  const accountTypeSelect = document.getElementById('accountType');
  const regularContent = document.getElementById('regularContent');
  const sellerCentralContent = document.getElementById('sellerCentralContent');
  const taxStartDateInput = document.getElementById('taxStartDate');
  const taxEndDateInput = document.getElementById('taxEndDate');
  const downloadTaxBtn = document.getElementById('downloadTaxBtn');
  const checkTabsBtn = document.getElementById('checkTabsBtn');
  const mainContent = document.getElementById('mainContent');
  const settingsContent = document.getElementById('settingsContent');
  const settingsIcon = document.getElementById('settingsIcon');
  const backBtn = document.getElementById('backBtn');
  const logoutBtn = document.getElementById('logoutBtn');
  const resetLicenseBtn = document.getElementById('resetLicenseBtn');
  const licenseCodeElement = document.getElementById('licenseCode');
  const licenseContent = document.getElementById('licenseContent');
  const licenseInput = document.getElementById('licenseInput');
  const activateBtn = document.getElementById('activateBtn');
  const licenseStatus = document.getElementById('licenseStatus');

  const oneYearAgo = new Date();
  oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
  const today = new Date().toISOString().split('T')[0];
  const oneYearAgoStr = oneYearAgo.toISOString().split('T')[0];

  startDateInput.min = oneYearAgoStr;
  startDateInput.max = today;
  endDateInput.min = oneYearAgoStr;
  endDateInput.max = today;
  endDateInput.value = today; // Set default end date to today

  taxStartDateInput.min = oneYearAgoStr;
  taxStartDateInput.max = today;
  taxEndDateInput.min = oneYearAgoStr;
  taxEndDateInput.max = today;
  taxEndDateInput.value = today; // Set default end date to today

  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    const currentUrl = tabs[0].url;
    if (currentUrl.includes('sellercentral.amazon.de')) {
      regularContent.style.display = 'none';
      sellerCentralContent.style.display = 'block';
    } else {
      regularContent.style.display = 'block';
      sellerCentralContent.style.display = 'none';
    }
  });

  downloadBtn.addEventListener('click', function() {
    if (!chrome.storage.sync.get('licenseKey')) {
      statusDiv.textContent = 'Please activate your license first.';
      return;
    }

    const startDate = startDateInput.value;
    const endDate = endDateInput.value;
    const accountType = accountTypeSelect.value;

    if (!startDate) {
      statusDiv.textContent = 'Please select a start date.';
      return;
    }

    if (!endDate) {
      statusDiv.textContent = 'Please select an end date.';
      return;
    }

    if (new Date(startDate) > new Date(endDate)) {
      statusDiv.textContent = 'Start date must be before or equal to end date.';
      return;
    }

    statusDiv.textContent = 'Download in progress...';
    downloadBtn.disabled = true;  // Voeg deze regel toe

    chrome.storage.local.set({ startDate: startDate, endDate: endDate, accountType: accountType }, function() {
      console.log('StartDate, endDate and accountType saved:', startDate, endDate, accountType);
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: "startDownload",
          startDate: startDate,
          endDate: endDate,
          accountType: accountType
        }, function(response) {
          if (chrome.runtime.lastError) {
            console.error('Error starting download:', chrome.runtime.lastError);
            statusDiv.textContent = 'If the download did not start. Please try again.';
            downloadBtn.disabled = false;  // Voeg deze regel toe
          }
        });
      });
    });
  });

  downloadTaxBtn.addEventListener('click', function() {
    if (!chrome.storage.sync.get('licenseKey')) {
      statusDiv.textContent = 'Please activate your license first.';
      return;
    }
    
    checkContentScriptLoaded((isLoaded) => {
      if (isLoaded) {
        startDownload();
      } else {
        console.log('Content script not detected, trying to reload...');
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
          chrome.tabs.reload(tabs[0].id, {}, function() {
            setTimeout(() => {
              checkContentScriptLoaded((isLoadedAfterReload) => {
                if (isLoadedAfterReload) {
                  startDownload();
                } else {
                  console.error('Content script could not be loaded');
                  statusDiv.textContent = 'Error: Content script not loaded. Please refresh the page manually and try again.';
                }
              });
            }, 2000);
          });
        });
      }
    });
  });

  function checkContentScriptLoaded(callback, attempts = 0) {
    const maxAttempts = 5;
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs.length === 0) {
        console.error('No active tab found');
        callback(false);
        return;
      }
      
      const currentUrl = tabs[0].url;
      if (!currentUrl.startsWith('https://sellercentral.amazon.de/') && 
          !currentUrl.includes('amazon.com') && 
          !currentUrl.includes('amazon.de') && 
          !currentUrl.includes('amazon.nl') && 
          !currentUrl.includes('amazon.fr') && 
          !currentUrl.includes('amazon.co.uk') && 
          !currentUrl.includes('amazon.it')) {
        console.error('Not on a supported Amazon page');
        callback(false);
        return;
      }
      
      chrome.runtime.sendMessage({action: "checkContentScriptLoaded"}, function(response) {
        if (chrome.runtime.lastError) {
          console.error('Error checking content script:', chrome.runtime.lastError);
          if (attempts < maxAttempts) {
            setTimeout(() => checkContentScriptLoaded(callback, attempts + 1), 1000);
          } else {
            console.error('Content script could not be loaded after multiple attempts');
            callback(false);
          }
        } else if (!response || !response.loaded) {
          console.log(`Content script not loaded yet, attempt ${attempts + 1} of ${maxAttempts}...`);
          if (attempts < maxAttempts) {
            setTimeout(() => checkContentScriptLoaded(callback, attempts + 1), 1000);
          } else {
            console.error('Content script could not be loaded after multiple attempts');
            callback(false);
          }
        } else {
          console.log('Content script loaded');
          callback(true);
        }
      });
    });
  }

  function startTaxDownload() {
    const taxStartDate = taxStartDateInput.value;
    const taxEndDate = taxEndDateInput.value;

    if (!taxStartDate) {
      statusDiv.textContent = 'Please select a start date for tax invoices.';
      return;
    }

    if (!taxEndDate) {
      statusDiv.textContent = 'Please select an end date for tax invoices.';
      return;
    }

    if (new Date(taxStartDate) > new Date(taxEndDate)) {
      statusDiv.textContent = 'Start date must be before or equal to end date.';
      return;
    }

    statusDiv.textContent = 'Download in Progress...';

    chrome.runtime.sendMessage({
      action: "startTaxDownload",
      startDate: taxStartDate,
      endDate: taxEndDate
    }, function(response) {
      if (chrome.runtime.lastError) {
        console.error('Error starting tax download:', chrome.runtime.lastError);
      } else if (response && response.error) {
        console.error('Error starting tax download:', response.error);
      } else {
        console.log('Tax download started');
      }
    });
  }

  function startDownload() {
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      const currentUrl = tabs[0].url;
      if (currentUrl.startsWith('https://sellercentral.amazon.de/')) {
        startTaxDownload();
      } else {
        startRegularDownload();
      }
    });
  }

  checkTabsBtn.addEventListener('click', function() {
    console.log('Check tabs button clicked');
    chrome.tabs.query({}, function(tabs) {
      tabs.forEach((tab) => {
        if (tab.url && (tab.url.includes('amazon.com') || tab.url.includes('amazon.de') || 
            tab.url.includes('amazon.nl') || tab.url.includes('amazon.fr') || 
            tab.url.includes('amazon.co.uk') || tab.url.includes('amazon.it') ||
            tab.url.startsWith('https://sellercentral.amazon.de/'))) {
          console.log('Sending checkAndDownloadPDF message to tab:', tab.id);
          chrome.tabs.sendMessage(tab.id, { action: "checkAndDownloadPDF" }, function(response) {
            if (chrome.runtime.lastError) {
              console.log(`Error sending to tab ${tab.id}:`, chrome.runtime.lastError);
            } else if (response) {
              console.log(`Response received from tab ${tab.id}:`, response);
            }
          });
        }
      });
    });
  });

  if (settingsIcon) {
    settingsIcon.addEventListener('click', function() {
      if (mainContent) mainContent.style.display = 'none';
      if (settingsContent) settingsContent.style.display = 'block';
      loadLicenseInfo();
    });
  }

  if (backBtn) {
    backBtn.addEventListener('click', function() {
      if (settingsContent) settingsContent.style.display = 'none';
      if (mainContent) mainContent.style.display = 'block';
    });
  }

  if (logoutBtn) {
    logoutBtn.addEventListener('click', function() {
      // Implement logout functionality
      console.log('Logout clicked');
      // Clear user data and return to login page
    });
  }

  resetLicenseBtn.addEventListener('click', function() {
    console.log('Reset License clicked');
    chrome.storage.sync.get('licenseKey', function(data) {
      if (data.licenseKey) {
        chrome.runtime.sendMessage({action: "resetLicense", licenseKey: data.licenseKey}, function(response) {
          if (response.success) {
            console.log('License reset successful on Whop');
            chrome.storage.sync.remove('licenseKey', function() {
              if (chrome.runtime.lastError) {
                console.error('Error removing license key from storage:', chrome.runtime.lastError);
              } else {
                console.log('License key removed from local storage');
                
                // Reset de licentiecode in de UI
                const licenseCodeElement = document.getElementById('licenseCode');
                if (licenseCodeElement) {
                  licenseCodeElement.textContent = '';
                }
                
                // Verberg de licentiestatus
                const licenseStatus = document.getElementById('licenseStatus');
                if (licenseStatus) {
                  licenseStatus.textContent = '';
                }
                
                showLicenseContent();
              }
            });
          } else {
            console.error('Error resetting license on Whop:', response.errorMessage);
            // Toon een foutmelding aan de gebruiker
            alert('Er is een fout opgetreden bij het resetten van de licentie: ' + response.errorMessage);
          }
        });
      } else {
        console.log('No license key to reset');
        showLicenseContent();
      }
    });
  });

  function loadLicenseInfo() {
    chrome.storage.sync.get(['licenseKey'], function(result) {
      const licenseCodeElement = document.getElementById('licenseCode');
      if (result.licenseKey) {
        const deobfuscatedLicenseKey = deobfuscateLicenseKey(result.licenseKey);
        licenseCodeElement.textContent = deobfuscatedLicenseKey;
      } else {
        licenseCodeElement.textContent = 'Not available';
      }
    });
  }

  function deobfuscateLicenseKey(obfuscatedKey) {
    const parts = obfuscatedKey.split('_');
    const base64 = parts.reverse().join('');
    return atob(base64);
  }

  function updateAccountTypeUI(isBusinessAccount) {
    if (accountTypeSelect) {
      accountTypeSelect.value = isBusinessAccount ? 'business' : 'nonbusiness';
      accountTypeSelect.disabled = true;
    }
  }

  chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
    if (tabs[0]) {
      chrome.tabs.sendMessage(tabs[0].id, {action: "getAccountType"}, function(response) {
        if (chrome.runtime.lastError) {
          console.error('Error getting account type:', chrome.runtime.lastError);
        } else if (response && response.isBusinessAccount !== undefined) {
          updateAccountTypeUI(response.isBusinessAccount);
        }
      });
    }
  });

  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "updateAccountType") {
      updateAccountTypeUI(request.isBusinessAccount);
    }
  });

  chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
    console.log('Message received in popup:', request);
    if (request.action === "updateProgress") {
      const progress = request.progress;
      if (typeof progress === 'number') {
        document.getElementById('progress').textContent = `Voortgang: ${progress.toFixed(2)}%`;
      } else {
        console.error('Ongeldige voortgangswaarde ontvangen:', progress);
      }
    } else if (request.action === "downloadComplete") {
      statusDiv.textContent = 'Download complete!';
    } else if (request.action === "downloadError") {
      statusDiv.textContent = `Error: ${request.error}`;
    } else if (request.action === "updateTaxProgress") {
      progressBar.value = request.progress;
      progressText.textContent = `${request.progress.toFixed(2)}%`;
    } else if (request.action === "taxDownloadComplete") {
      statusDiv.textContent = 'Tax invoice download complete!';
    }
  });

  chrome.storage.sync.get('licenseKey', function(data) {
    if (data.licenseKey) {
      showMainContent();
      loadLicenseInfo();
    } else {
      showLicenseContent();
    }
  });

  activateBtn.addEventListener('click', function() {
    const licenseKey = licenseInput.value.trim();
    if (licenseKey) {
      const obfuscatedLicenseKey = obfuscateLicenseKey(licenseKey);
      chrome.runtime.sendMessage({action: "validateLicense", licenseKey: obfuscatedLicenseKey}, function(response) {
        if (response.isValid) {
          chrome.storage.sync.set({licenseKey: obfuscatedLicenseKey}, function() {
            licenseStatus.textContent = "License activated successfully!";
            licenseStatus.style.color = "green";
            loadLicenseInfo();
            setTimeout(showMainContent, 1500);
          });
        } else {
          licenseStatus.textContent = "Invalid license. Please try again.";
          licenseStatus.style.color = "red";
        }
      });
    } else {
      licenseStatus.textContent = "Please enter a license key.";
      licenseStatus.style.color = "red";
    }
  });

  function showLicenseContent() {
    licenseContent.style.display = 'block';
    mainContent.style.display = 'none';
    settingsContent.style.display = 'none';
  
    // Reset de waarde van het licentiecode invoerveld
    const licenseInput = document.getElementById('licenseInput');
    if (licenseInput) {
      licenseInput.value = '';
    }
  
    // Verberg de licentiestatus
    const licenseStatus = document.getElementById('licenseStatus');
    if (licenseStatus) {
      licenseStatus.textContent = '';
    }
  }

  function showMainContent() {
    licenseContent.style.display = 'none';
    mainContent.style.display = 'block';
  }
});
// START_OBFUSCATE
function obfuscateLicenseKey(key) {
  const base64 = btoa(key);
  const parts = base64.match(/.{1,4}/g) || [];
  return parts.reverse().join('_');
}

function deobfuscateLicenseKey(obfuscatedKey) {
  const parts = obfuscatedKey.split('_');
  const base64 = parts.reverse().join('');
  return atob(base64);
}
// END_OBFUSCATE