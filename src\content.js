const amazonInvoiceDownloader = window.amazonInvoiceDownloader;
console.log('Amazon Invoice Downloader content script loaded on:', window.location.href);

let isBusinessAccount = true;
let startDate;
let endDate;
let downloadedInvoices = 0;
let totalInvoices = 0;

console.log('Content script loaded');

// Function to check if nonBusinessInvoices is loaded
function checkNonBusinessInvoicesLoaded(callback, attempts = 0) {
  const maxAttempts = 20;
  if (window.nonBusinessInvoices) {
    console.log('nonBusinessInvoices successfully loaded');
    callback(true);
  } else if (attempts < maxAttempts) {
    console.log(`Attempt ${attempts + 1} to load nonBusinessInvoices`);
    setTimeout(() => checkNonBusinessInvoicesLoaded(callback, attempts + 1), 1000);
  } else {
    console.error('nonBusinessInvoices could not be loaded after multiple attempts');
    callback(false);
  }
}

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  console.log('Message received in content script:', request);

  if (request.action === "startDownload") {
    console.log('StartDownload message received:', request);
    startDate = normalizeDate(new Date(request.startDate));
    endDate = normalizeDate(new Date(request.endDate));
    isBusinessAccount = request.accountType === 'business';

    console.log('Normalized start date:', startDate.toISOString());
    console.log('Normalized end date:', endDate.toISOString());

    if (isBusinessAccount) {
      initBusinessDownload(startDate, endDate);
    } else {
      checkNonBusinessInvoicesLoaded((loaded) => {
        if (loaded) {
          window.nonBusinessInvoices.initNonBusinessDownload(startDate, endDate);
        } else {
          console.error('Could not load nonBusinessInvoices. Download cannot start.');
          chrome.runtime.sendMessage({ action: "downloadError", error: "Could not load nonBusinessInvoices" });
        }
      });
    }
  } else if (request.action === "startTaxDownload") {
    console.log('StartTaxDownload message received:', request);
    initTaxDownload(request.startDate);
  } else if (request.action === "processPage") {
    console.log('ProcessPage message received');
    if (isBusinessAccount) {
      processBusinessPage();
    } else {
      checkNonBusinessInvoicesLoaded((loaded) => {
        if (loaded) {
          window.nonBusinessInvoices.processNonBusinessPage();
        } else {
          console.error('Could not load nonBusinessInvoices. Cannot process page.');
          chrome.runtime.sendMessage({ action: "downloadError", error: "Could not load nonBusinessInvoices" });
        }
      });
    }
  } else if (request.action === "ping") {
    sendResponse({pong: true});
  }

  return true; // Indicates that we will respond asynchronously
});

// Detect account type and marketplace
// START_OBFUSCATE
function detectAccountType() {
  if (window.location.href.startsWith('https://sellercentral')) {
    console.log('Seller Central detected, account type detection skipped');
    return;
  }
  
  const businessAccountIndicator = document.querySelector('a[href*="ref_=abn_logo"]');
  const isBusinessAccount = !!businessAccountIndicator;
  console.log(`Account type detected: ${isBusinessAccount ? 'Business' : 'Non-business'}`);
  chrome.runtime.sendMessage({ action: "updateAccountType", isBusinessAccount: isBusinessAccount });
}
// END_OBFUSCATE
// Run detection when the page loads
window.addEventListener('load', detectAccountType);

// Listen for messages from the popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === "getAccountType") {
    const businessAccountIndicator = document.querySelector('a[href*="ref_=abn_logo"]');
    const isBusinessAccount = !!businessAccountIndicator;
    sendResponse({ isBusinessAccount: isBusinessAccount });
  }
});

function initBusinessDownload(initialStartDate, initialEndDate) {
  startDate = new Date(initialStartDate);
  endDate = new Date(initialEndDate);
  const normalizedStartDate = new Date(startDate.getFullYear(), startDate.getMonth(), startDate.getDate());
  const normalizedEndDate = new Date(endDate.getFullYear(), endDate.getMonth(), endDate.getDate());
  console.log('Normalized start date:', normalizedStartDate);
  console.log('Normalized end date:', normalizedEndDate);
  processBusinessPage(normalizedStartDate, normalizedEndDate);
}

function processBusinessPage() {
  console.log('Processing business page');
  const result = findInvoiceLinks();
  if (result.links.length > 0) {
    totalInvoices += result.links.length;
    downloadInvoices(result.links).then(() => {
      if (!result.startDateReached) {
        goToNextPage();
      } else {
        console.log('All invoices have been downloaded');
        chrome.runtime.sendMessage({ action: "downloadComplete" });
      }
    });
  } else if (!result.startDateReached) {
    goToNextPage();
  } else {
    console.log('No invoices found and start date reached');
    chrome.runtime.sendMessage({ action: "downloadComplete" });
  }
}

function findInvoiceLinks() {
  const marketplace = detectMarketplace();
  const config = window.amazonInvoiceDownloader.marketplaceConfig[marketplace];
  if (!config) {
    console.error('No configuration found for marketplace:', marketplace);
    return { links: [], startDateReached: false };
  }
  const invoiceLinks = [];
  let pageStartDateReached = false;

  console.log('Normalized start date at beginning of findInvoiceLinks:', startDate.toISOString());

  const orderBlocks = document.querySelectorAll('.a-box.a-color-offset-background.order-info');
  console.log(`Number of order blocks found: ${orderBlocks.length}`);

  orderBlocks.forEach((block, blockIndex) => {
    console.log(`Processing order block ${blockIndex + 1}`);
    
    const dateElement = block.querySelector(config.dateSelector);
    const orderDate = dateElement ? dateElement.textContent.trim() : 'Not found';
    console.log(`Order date: ${orderDate}`);

    const parsedOrderDate = normalizeDate(parseDate(orderDate, config.monthNames));
    console.log('StartDate type:', typeof startDate);
    console.log('StartDate value:', startDate.toISOString());
    console.log('EndDate type:', typeof endDate);
    console.log('EndDate value:', endDate.toISOString());
    console.log('ParsedOrderDate type:', typeof parsedOrderDate);
    console.log('ParsedOrderDate value:', parsedOrderDate.toISOString());

    if (parsedOrderDate < startDate) {
      console.log('Start date reached, stopping search');
      pageStartDateReached = true;
      return;
    }

    if (parsedOrderDate > endDate) {
      console.log('Order date is after end date, skipping this order');
      return;
    }

    const orderNumberElement = block.querySelector(config.orderNumberSelector);
    const orderNumber = orderNumberElement ? orderNumberElement.textContent.trim() : 'Not found';
    console.log(`Order number: ${orderNumber}`);

    const popoverTriggers = block.querySelectorAll('a.a-popover-trigger.a-declarative');
    console.log(`Number of popover triggers found: ${popoverTriggers.length}`);
    
    if (popoverTriggers.length > 0) {
      popoverTriggers.forEach((trigger, triggerIndex) => {
        console.log(`Popover trigger ${triggerIndex + 1} found:`, trigger.textContent.trim());
        
        if (Array.isArray(config.invoiceText) ? config.invoiceText.includes(trigger.textContent.trim()) : config.invoiceText === trigger.textContent.trim()) {
          console.log(`Invoice trigger found: ${trigger.textContent.trim()}`);
          
          const parentSpan = trigger.closest('span.a-declarative');
          if (parentSpan) {
            const popoverData = parentSpan.getAttribute('data-a-popover');
            if (popoverData) {
              try {
                const popoverConfig = JSON.parse(popoverData);
                if (popoverConfig.url) {
                  const fullUrl = new URL(popoverConfig.url, window.location.origin).href;
                  console.log(`Found invoice URL: ${fullUrl}`);
                  
                  invoiceLinks.push({
                    date: orderDate,
                    orderNumber: orderNumber,
                    url: fullUrl,
                    text: trigger.textContent.trim()
                  });
                  console.log(`Invoice link added for order block ${blockIndex + 1}`);
                } else {
                  console.log(`No URL found in popover configuration for trigger ${triggerIndex + 1}`);
                }
              } catch (error) {
                console.error('Error parsing popover data:', error);
              }
            } else {
              console.log(`No data-a-popover attribute found for parent span of trigger ${triggerIndex + 1}`);
            }
          } else {
            console.log(`No parent span.a-declarative found for trigger ${triggerIndex + 1}`);
          }
        }
      });
    } else {
      console.log('No popover triggers found for this order block');
    }

    console.log(`Number of invoice links found for order block ${blockIndex + 1}: ${invoiceLinks.length}`);
  });

  console.log(`Total number of invoice links found: ${invoiceLinks.length}`);
  return { links: invoiceLinks, startDateReached: pageStartDateReached };
}

function downloadInvoices(invoiceLinks) {
  console.log(`Starting download of ${invoiceLinks.length} invoices`);
  return new Promise((resolve) => {
    let downloadedCount = 0;
    invoiceLinks.forEach((linkInfo, index) => {
      setTimeout(() => {
        downloadInvoice(linkInfo).then(() => {
          downloadedCount++;
          if (downloadedCount === invoiceLinks.length) {
            resolve();
          }
        });
      }, index * 1000);
    });
  });
}

function downloadInvoice(linkInfo) {
  return new Promise((resolve) => {
    chrome.runtime.sendMessage({
      action: "fetchInvoicePage",
      url: linkInfo.url
    }, response => {
      if (response && response.html) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(response.html, 'text/html');
        const pdfLinks = doc.querySelectorAll('a[href*="/documents/download/"][href$=".pdf"]');
        
        if (pdfLinks.length > 0) {
          const downloadPromises = Array.from(pdfLinks).map((pdfLink, index) => {
            return new Promise((downloadResolve) => {
              const pdfUrl = new URL(pdfLink.getAttribute('href'), window.location.origin).href;
              console.log(`Found PDF link ${index + 1}: ${pdfUrl}`);
              
              const safeOrderNumber = linkInfo.orderNumber.replace(/[^0-9-]/g, '');
              const safeLinkText = pdfLink.textContent.trim().replace(/[^a-z0-9]/gi, '_').toLowerCase();
              const fileName = `${safeOrderNumber}_${safeLinkText}.pdf`;
              
              chrome.runtime.sendMessage({
                action: "downloadBusinessPDF",
                url: pdfUrl,
                filename: fileName
              }, downloadResponse => {
                if (chrome.runtime.lastError) {
                  console.error('Error sending downloadBusinessPDF request:', chrome.runtime.lastError);
                } else if (downloadResponse && downloadResponse.error) {
                  console.error('Error downloading PDF:', downloadResponse.error);
                } else if (downloadResponse && downloadResponse.success) {
                  console.log(`PDF download ${index + 1} started with ID:`, downloadResponse.downloadId);
                } else {
                  console.error('Unexpected response while downloading PDF:', downloadResponse);
                }
                
                downloadedInvoices++;
                updateProgress();
                downloadResolve();
              });
            });
          });

          Promise.all(downloadPromises).then(() => {
            resolve();
          });
        } else {
          console.error('No PDF links found in the invoice page');
          resolve();
        }
      } else if (response && response.error) {
        console.error('Error fetching the invoice page:', response.error);
        resolve();
      } else {
        console.error('Unexpected response while fetching the invoice page');
        resolve();
      }
    });
  });
}

function updateProgress() {
  const progress = (downloadedInvoices / totalInvoices) * 100;
  console.log(`Progress: ${progress.toFixed(2)}%`);
  chrome.runtime.sendMessage({
    action: "updateProgress",
    progress: progress
  });
}

function goToNextPage(startDateReached) {
  if (startDateReached) {
    console.log('Start date reached, download completed');
    chrome.runtime.sendMessage({action: "downloadComplete"});
    return;
  }

  const pagination = document.querySelector('.a-pagination');
  if (pagination) {
    const currentPageItem = pagination.querySelector('li.a-selected');
    if (currentPageItem) {
      const nextPageItem = currentPageItem.nextElementSibling;
      if (nextPageItem && !nextPageItem.classList.contains('a-disabled')) {
        const nextPageLink = nextPageItem.querySelector('a');
        if (nextPageLink) {
          currentPage++;
          console.log(`Navigating to page ${currentPage}`);
          chrome.runtime.sendMessage({action: "navigateToNextPage", url: nextPageLink.href});
          return;
        }
      }
    }
  }
  
  console.log('No next page found or last page reached');
  chrome.runtime.sendMessage({action: "downloadComplete"});
}

function isValidDate(d) {
  return d instanceof Date && !isNaN(d);
}

// Run initial check when the page loads
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', checkInitialState);
} else {
  checkInitialState();
}

function checkInitialState() {
  if (isOrderHistoryPage()) {
    console.log('On the correct page, waiting for startDownload message');
  } else {
    console.log('Not on the correct page, extension is inactive');
  }
}

function isOrderHistoryPage() {
  return window.location.href.includes('/order-history') || 
         window.location.href.includes('/your-orders/orders') ||
         document.querySelector('.your-orders-content-container, .your-orders-page') !== null;
}

function normalizeDate(date) {
  return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
}