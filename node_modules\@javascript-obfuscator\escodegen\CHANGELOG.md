v2.2.2
---
* Fixed precedence of `export default` IIFE
* 
v2.2.1
---
* Fixed generation of private identifier names

v2.2.0
---
* Added support for ES2022 class fields and private properties

v2.1.1
---
* Removed `bin` section from `package.json`

v2.1.0
---
* Created the fork of `escodegen` for `javascript-obfuscator` package
* `nullish-coalescing` support. Original PR: https://github.com/estools/escodegen/pull/417
* `bigint` and `numeric-separators` support. Original PR: https://github.com/estools/escodegen/pull/421
* Support for `exported` field of `ExportAllDeclaration` node. Original PR: https://github.com/estools/escodegen/pull/420