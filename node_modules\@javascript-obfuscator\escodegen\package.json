{"name": "@javascript-obfuscator/escodegen", "description": "`escodegen` fork for `javascript-obfuscator``", "homepage": "http://github.com/estools/escodegen", "main": "escodegen.js", "files": ["LICENSE.BSD", "README.md", "bin", "escodegen.js", "package.json"], "version": "2.3.0", "engines": {"node": ">=6.0"}, "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "web": "http://github.com/Constellation"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "web": "https://github.com/sanex3339"}], "repository": {"type": "git", "url": "http://github.com/estools/escodegen.git"}, "dependencies": {"@javascript-obfuscator/estraverse": "^5.3.0", "esutils": "^2.0.2", "esprima": "^4.0.1", "optionator": "^0.8.1"}, "optionalDependencies": {"source-map": "~0.6.1"}, "devDependencies": {"acorn": "^8.7.0", "bluebird": "^3.4.7", "bower-registry-client": "^1.0.0", "chai": "^4.2.0", "chai-exclude": "^2.0.2", "commonjs-everywhere": "^0.9.7", "gulp": "^3.8.10", "gulp-eslint": "^3.0.1", "gulp-mocha": "^3.0.1", "semver": "^5.1.0"}, "license": "BSD-2-<PERSON><PERSON>", "scripts": {"test": "gulp travis", "unit-test": "gulp test", "lint": "gulp lint"}}