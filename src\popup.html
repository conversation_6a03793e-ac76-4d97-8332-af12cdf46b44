<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Deal Detectives</title>
  <link rel="stylesheet" href="popup.css">
</head>
<body>
  <div id="header">
    <img src="images/logo.png" alt="Deal Detectives Logo" id="logo">
    <h1>The Deal Detectives</h1>
  </div>
  <div id="mainContent">
    <div id="regularContent">
      <h2>Invoices Downloader</h2>
      <div class="input-group">
        <label for="startDate">Start date:</label>
        <input type="date" id="startDate" required>
      </div>
      <div class="input-group">
        <label for="endDate">End date:</label>
        <input type="date" id="endDate" required>
      </div>
      <div class="input-group">
        <label for="accountType">Account type:</label>
        <select id="accountType">
          <option value="business">Business</option>
          <option value="nonbusiness">Consumer</option>
        </select>
      </div>
      <button id="downloadBtn">Download</button>
      <div id="progress" class="hidden">
        <progress id="progressBar" value="0" max="100"></progress>
        <span id="progressText">0%</span>
      </div>
    </div>
    
    <div id="sellerCentralContent" style="display: none;">
      <h1>Amazon Tax Invoices Downloader</h1>
      <div class="input-group">
        <label for="taxStartDate">Start date:</label>
        <input type="date" id="taxStartDate" required>
      </div>
      <div class="input-group">
        <label for="taxEndDate">End date:</label>
        <input type="date" id="taxEndDate" required>
      </div>
      <button id="downloadTaxBtn">Open Tax Invoices</button>
      <button id="checkTabsBtn">Download Tax Invoices</button>
    </div>
    
    <div id="status"></div>
    <div id="settingsIconContainer">
      <img src="images/settings-icon.png" id="settingsIcon" alt="Settings">
    </div>
  </div>

  <div id="settingsContent" style="display: none;">
    <h1>Settings</h1>
    <div class="setting-group">
      <h2></h2>
      <div id="licenseBox">
        <p>Your license code:</p>
        <code id="licenseCode">Loading...</code>
      </div>
    </div>
    <div class="setting-group">
      <button id="resetLicenseBtn">Reset License</button>
    </div>
    <button id="backBtn">Back to Main</button>
  </div>

  <div id="licenseContent" style="display: none;">
    <h1>Enter License</h1>
    <div class="input-group">
      <label for="licenseInput"></label>
      <input type="text" id="licenseInput" required>
    </div>
    <button id="activateBtn">Activate License</button>
    <div id="licenseStatus"></div>
  </div>

  <script src="popup.js"></script>
</body>
</html>