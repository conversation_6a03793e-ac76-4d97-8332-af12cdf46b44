{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AASvC,oFAAoF;AACpF,IAAM,oBAAoB,GAAG,CAAC,oBAAoB,EAAE,sBAAsB,CAAC,CAAC;AAE5E,kCAAkC;AAClC,IAAM,oBAAoB,GAAG,cAAc,CAAC;AAE5C;;GAEG;AACH,MAAM,UAAU,MAAM,CAAC,KAAa,EAAE,OAAqB;IAArB,wBAAA,EAAA,YAAqB;IAEvD,IAAA,KAIE,OAAO,YAJyB,EAAlC,WAAW,mBAAG,oBAAoB,KAAA,EAClC,KAGE,OAAO,YAHyB,EAAlC,WAAW,mBAAG,oBAAoB,KAAA,EAClC,KAEE,OAAO,UAFY,EAArB,SAAS,mBAAG,SAAS,KAAA,EACrB,KACE,OAAO,UADM,EAAf,SAAS,mBAAG,GAAG,KAAA,CACL;IAEZ,IAAI,MAAM,GAAG,OAAO,CAClB,OAAO,CAAC,KAAK,EAAE,WAAW,EAAE,QAAQ,CAAC,EACrC,WAAW,EACX,IAAI,CACL,CAAC;IACF,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC;IAExB,oDAAoD;IACpD,OAAO,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,IAAI;QAAE,KAAK,EAAE,CAAC;IAC9C,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI;QAAE,GAAG,EAAE,CAAC;IAE9C,sCAAsC;IACtC,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7E,CAAC;AAED;;GAEG;AACH,SAAS,OAAO,CAAC,KAAa,EAAE,EAAqB,EAAE,KAAa;IAClE,IAAI,EAAE,YAAY,MAAM;QAAE,OAAO,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1D,OAAO,EAAE,CAAC,MAAM,CAAC,UAAC,KAAK,EAAE,EAAE,IAAK,OAAA,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,CAAC,EAAxB,CAAwB,EAAE,KAAK,CAAC,CAAC;AACnE,CAAC", "sourcesContent": ["import { lowerCase } from \"lower-case\";\n\nexport interface Options {\n  splitRegexp?: RegExp | RegExp[];\n  stripRegexp?: RegExp | RegExp[];\n  delimiter?: string;\n  transform?: (part: string, index: number, parts: string[]) => string;\n}\n\n// Support camel case (\"camelCase\" -> \"camel Case\" and \"CAMELCase\" -> \"CAMEL Case\").\nconst DEFAULT_SPLIT_REGEXP = [/([a-z0-9])([A-Z])/g, /([A-Z])([A-Z][a-z])/g];\n\n// Remove all non-word characters.\nconst DEFAULT_STRIP_REGEXP = /[^A-Z0-9]+/gi;\n\n/**\n * Normalize the string into something other libraries can manipulate easier.\n */\nexport function noCase(input: string, options: Options = {}) {\n  const {\n    splitRegexp = DEFAULT_SPLIT_REGEXP,\n    stripRegexp = DEFAULT_STRIP_REGEXP,\n    transform = lowerCase,\n    delimiter = \" \",\n  } = options;\n\n  let result = replace(\n    replace(input, splitRegexp, \"$1\\0$2\"),\n    stripRegexp,\n    \"\\0\"\n  );\n  let start = 0;\n  let end = result.length;\n\n  // Trim the delimiter from around the output string.\n  while (result.charAt(start) === \"\\0\") start++;\n  while (result.charAt(end - 1) === \"\\0\") end--;\n\n  // Transform each token independently.\n  return result.slice(start, end).split(\"\\0\").map(transform).join(delimiter);\n}\n\n/**\n * Replace `re` in the input string with the replacement value.\n */\nfunction replace(input: string, re: RegExp | RegExp[], value: string) {\n  if (re instanceof RegExp) return input.replace(re, value);\n  return re.reduce((input, re) => input.replace(re, value), input);\n}\n"]}